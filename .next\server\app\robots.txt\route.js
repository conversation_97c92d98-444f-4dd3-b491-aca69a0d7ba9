(()=>{var a={};a.id=784,a.ids=[784],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12127:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{resolveManifest:function(){return g},resolveRobots:function(){return e},resolveRouteData:function(){return h},resolveSitemap:function(){return f}});let d=c(77341);function e(a){let b="";for(let c of Array.isArray(a.rules)?a.rules:[a.rules]){for(let a of(0,d.resolveArray)(c.userAgent||["*"]))b+=`User-Agent: ${a}
`;if(c.allow)for(let a of(0,d.resolveArray)(c.allow))b+=`Allow: ${a}
`;if(c.disallow)for(let a of(0,d.resolveArray)(c.disallow))b+=`Disallow: ${a}
`;c.crawlDelay&&(b+=`Crawl-delay: ${c.crawlDelay}
`),b+="\n"}return a.host&&(b+=`Host: ${a.host}
`),a.sitemap&&(0,d.resolveArray)(a.sitemap).forEach(a=>{b+=`Sitemap: ${a}
`}),b}function f(a){let b=a.some(a=>Object.keys(a.alternates??{}).length>0),c=a.some(a=>{var b;return!!(null==(b=a.images)?void 0:b.length)}),d=a.some(a=>{var b;return!!(null==(b=a.videos)?void 0:b.length)}),e="";for(let i of(e+='<?xml version="1.0" encoding="UTF-8"?>\n',e+='<urlset xmlns="https://www.sitemaps.org/schemas/sitemap/0.9"',c&&(e+=' xmlns:image="https://www.google.com/schemas/sitemap-image/1.1"'),d&&(e+=' xmlns:video="https://www.google.com/schemas/sitemap-video/1.1"'),b?e+=' xmlns:xhtml="https://www.w3.org/1999/xhtml">\n':e+=">\n",a)){var f,g,h;e+="<url>\n",e+=`<loc>${i.url}</loc>
`;let a=null==(f=i.alternates)?void 0:f.languages;if(a&&Object.keys(a).length)for(let b in a)e+=`<xhtml:link rel="alternate" hreflang="${b}" href="${a[b]}" />
`;if(null==(g=i.images)?void 0:g.length)for(let a of i.images)e+=`<image:image>
<image:loc>${a}</image:loc>
</image:image>
`;if(null==(h=i.videos)?void 0:h.length)for(let a of i.videos)e+=["<video:video>",`<video:title>${a.title}</video:title>`,`<video:thumbnail_loc>${a.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${a.description}</video:description>`,a.content_loc&&`<video:content_loc>${a.content_loc}</video:content_loc>`,a.player_loc&&`<video:player_loc>${a.player_loc}</video:player_loc>`,a.duration&&`<video:duration>${a.duration}</video:duration>`,a.view_count&&`<video:view_count>${a.view_count}</video:view_count>`,a.tag&&`<video:tag>${a.tag}</video:tag>`,a.rating&&`<video:rating>${a.rating}</video:rating>`,a.expiration_date&&`<video:expiration_date>${a.expiration_date}</video:expiration_date>`,a.publication_date&&`<video:publication_date>${a.publication_date}</video:publication_date>`,a.family_friendly&&`<video:family_friendly>${a.family_friendly}</video:family_friendly>`,a.requires_subscription&&`<video:requires_subscription>${a.requires_subscription}</video:requires_subscription>`,a.live&&`<video:live>${a.live}</video:live>`,a.restriction&&`<video:restriction relationship="${a.restriction.relationship}">${a.restriction.content}</video:restriction>`,a.platform&&`<video:platform relationship="${a.platform.relationship}">${a.platform.content}</video:platform>`,a.uploader&&`<video:uploader${a.uploader.info&&` info="${a.uploader.info}"`}>${a.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(i.lastModified){let a=i.lastModified instanceof Date?i.lastModified.toISOString():i.lastModified;e+=`<lastmod>${a}</lastmod>
`}i.changeFrequency&&(e+=`<changefreq>${i.changeFrequency}</changefreq>
`),"number"==typeof i.priority&&(e+=`<priority>${i.priority}</priority>
`),e+="</url>\n"}return e+"</urlset>\n"}function g(a){return JSON.stringify(a)}function h(a,b){return"robots"===b?e(a):"sitemap"===b?f(a):"manifest"===b?g(a):""}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77341:(a,b)=>{"use strict";function c(a){return Array.isArray(a)?a:[a]}function d(a){if(null!=a)return c(a)}function e(a){let b;if("string"==typeof a)try{b=(a=new URL(a)).origin}catch{}return b}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{getOrigin:function(){return e},resolveArray:function(){return c},resolveAsArrayOrUndefined:function(){return d}})},78335:()=>{},83060:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>C,patchFetch:()=>B,routeModule:()=>x,serverHooks:()=>A,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>z});var d={};c.r(d),c.d(d,{GET:()=>w});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(12127);async function w(){let a=await {rules:{userAgent:"*",allow:"/",disallow:"/private/"},sitemap:"https://fitzonebiratnagar.com/sitemap.xml"},b=(0,v.resolveRouteData)(a,"robots");return new u.NextResponse(b,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let x=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},distDir:".next",projectDir:"",resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5CAdmin%5CDesktop%5Cashish%5Cgym%5Csrc%5Capp%5Crobots.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:d}),{workAsyncStorage:y,workUnitAsyncStorage:z,serverHooks:A}=x;function B(){return(0,g.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:z})}async function C(a,b,c){var d;let e="/robots.txt/route";"/index"===e&&(e="/");let g=await x.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:y,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!y){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||x.isDev||y||(G="/index"===(G=D)?"/":G);let H=!0===x.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>x.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>x.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await x.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await x.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),y&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await x.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=83060));module.exports=c})();