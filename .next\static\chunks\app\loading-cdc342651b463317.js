(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[209],{465:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("dumbbell",[["path",{d:"M17.596 12.768a2 2 0 1 0 2.829-2.829l-1.768-1.767a2 2 0 0 0 2.828-2.829l-2.828-2.828a2 2 0 0 0-2.829 2.828l-1.767-1.768a2 2 0 1 0-2.829 2.829z",key:"9m4mmf"}],["path",{d:"m2.5 21.5 1.4-1.4",key:"17g3f0"}],["path",{d:"m20.1 3.9 1.4-1.4",key:"1qn309"}],["path",{d:"M5.343 21.485a2 2 0 1 0 2.829-2.828l1.767 1.768a2 2 0 1 0 2.829-2.829l-6.364-6.364a2 2 0 1 0-2.829 2.829l1.768 1.767a2 2 0 0 0-2.828 2.829z",key:"1t2c92"}],["path",{d:"m9.6 14.4 4.8-4.8",key:"6umqxw"}]])},3140:(e,a,t)=>{Promise.resolve().then(t.bind(t,7023))},7023:(e,a,t)=>{"use strict";t.d(a,{default:()=>l});var i=t(5155),s=t(2984),n=t(465);function l(){return(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-white",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(s.P.div,{animate:{rotate:360,scale:[1,1.2,1]},transition:{rotate:{duration:2,repeat:1/0,ease:"linear"},scale:{duration:1,repeat:1/0,ease:"easeInOut"}},className:"inline-block mb-4",children:(0,i.jsx)(n.A,{className:"w-12 h-12 text-orange-600"})}),(0,i.jsx)(s.P.h2,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},className:"text-xl font-semibold text-gray-900 mb-2",children:"FitZone Biratnagar"}),(0,i.jsx)(s.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"flex space-x-1 justify-center",children:[0,1,2].map(e=>(0,i.jsx)(s.P.div,{animate:{y:[0,-10,0],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,delay:.2*e},className:"w-2 h-2 bg-orange-600 rounded-full"},e))})]})})}}},e=>{e.O(0,[813,441,964,358],()=>e(e.s=3140)),_N_E=e.O()}]);