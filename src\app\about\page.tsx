'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import SectionTitle from '@/components/sections/SectionTitle';
import { gymInfo, trainers } from '@/data';
import { Award, Users, Target, Heart } from 'lucide-react';

export default function AboutPage() {
  const values = [
    {
      icon: Target,
      title: 'Excellence',
      description: 'We strive for excellence in everything we do, from our equipment to our training programs.'
    },
    {
      icon: Users,
      title: 'Community',
      description: 'Building a supportive community where everyone feels welcome and motivated to achieve their goals.'
    },
    {
      icon: Heart,
      title: 'Wellness',
      description: 'Promoting holistic wellness that encompasses physical, mental, and emotional health.'
    },
    {
      icon: Award,
      title: 'Results',
      description: 'Committed to helping our members achieve real, lasting results through proven methods.'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold mb-6"
          >
            About {gymInfo.name}
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Established in {gymInfo.established}, we&apos;ve been Biratnagar&apos;s premier fitness destination,
            helping over {gymInfo.members} achieve their health and wellness goals.
          </motion.p>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionTitle
            title="Our Story"
            subtitle="From humble beginnings to Biratnagar's leading fitness center"
          />
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center mb-6">
                <span className="text-gray-500">Gym Exterior Photo</span>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <p className="text-lg text-gray-600">
                {gymInfo.name} was born from a simple vision: to create a fitness center that truly serves
                the Biratnagar community. Founded in {gymInfo.established}, we started with a commitment to
                providing world-class fitness facilities in the heart of our beloved city.
              </p>
              <p className="text-lg text-gray-600">
                Over the years, we&apos;ve grown from a small local gym to Biratnagar&apos;s most trusted fitness
                destination. Our success is measured not just in the modern equipment we&apos;ve acquired or
                the facilities we&apos;ve built, but in the lives we&apos;ve transformed and the community we&apos;ve created.
              </p>
              <p className="text-lg text-gray-600">
                Today, with over {gymInfo.members} and a team of {gymInfo.trainers} certified trainers,
                we continue to evolve and adapt to meet the changing needs of our community while staying
                true to our core mission: {gymInfo.mission}
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionTitle
            title="Our Values"
            subtitle="The principles that guide everything we do"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full text-center hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4">
                      <value.icon className="w-8 h-8 text-orange-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">{value.title}</h3>
                    <p className="text-gray-600">{value.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Preview */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionTitle
            title="Meet Our Team"
            subtitle="Experienced professionals dedicated to your success"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {trainers.slice(0, 3).map((trainer, index) => (
              <motion.div
                key={trainer.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6 text-center">
                    <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <span className="text-gray-500 text-sm font-semibold">
                        {trainer.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{trainer.name}</h3>
                    <p className="text-orange-600 font-medium mb-3">{trainer.experience} Experience</p>
                    <p className="text-gray-600 text-sm mb-4">{trainer.bio.substring(0, 100)}...</p>
                    <div className="flex flex-wrap gap-2 justify-center">
                      {trainer.specialization.slice(0, 2).map((spec, i) => (
                        <span key={i} className="px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full">
                          {spec}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <a
              href="/trainers"
              className="inline-flex items-center px-6 py-3 border border-orange-600 text-orange-600 font-medium rounded-lg hover:bg-orange-600 hover:text-white transition-colors duration-200"
            >
              Meet All Our Trainers
            </a>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
