"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[322],{3109:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},4186:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5868:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6380:(e,t,r)=>{r.d(t,{UC:()=>eh,Y9:()=>eu,q7:()=>ec,bL:()=>ed,l9:()=>ef});var i,n=r(2115),l=r(6081);function s(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function o(e,t){var r=s(e,t,"get");return r.get?r.get.call(e):r.value}function a(e,t,r){var i=s(e,t,"set");if(i.set)i.set.call(e,r);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=r}return r}var d=r(6101),c=r(9708),u=r(5155),f=new WeakMap;function h(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,i=p(t),n=i>=0?i:r+i;return n<0||n>=r?-1:n}(e,t);return -1===r?void 0:e[r]}function p(e){return e!=e||0===e?0:Math.trunc(e)}i=new WeakMap,class e extends Map{set(e,t){return f.get(this)&&(this.has(e)?o(this,i)[o(this,i).indexOf(e)]=e:o(this,i).push(e)),super.set(e,t),this}insert(e,t,r){let n,l=this.has(t),s=o(this,i).length,a=p(e),d=a>=0?a:s+a,c=d<0||d>=s?-1:d;if(c===this.size||l&&c===this.size-1||-1===c)return this.set(t,r),this;let u=this.size+ +!l;a<0&&d++;let f=[...o(this,i)],h=!1;for(let e=d;e<u;e++)if(d===e){let i=f[e];f[e]===t&&(i=f[e+1]),l&&this.delete(t),n=this.get(i),this.set(t,r)}else{h||f[e-1]!==t||(h=!0);let r=f[h?e:e-1],i=n;n=this.get(r),this.delete(r),this.set(r,i)}return this}with(t,r,i){let n=new e(this);return n.insert(t,r,i),n}before(e){let t=o(this,i).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,r){let n=o(this,i).indexOf(e);return -1===n?this:this.insert(n,t,r)}after(e){let t=o(this,i).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,r){let n=o(this,i).indexOf(e);return -1===n?this:this.insert(n+1,t,r)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return a(this,i,[]),super.clear()}delete(e){let t=super.delete(e);return t&&o(this,i).splice(o(this,i).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=h(o(this,i),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=h(o(this,i),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return o(this,i).indexOf(e)}keyAt(e){return h(o(this,i),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let i=r+t;return i<0&&(i=0),i>=this.size&&(i=this.size-1),this.at(i)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let i=r+t;return i<0&&(i=0),i>=this.size&&(i=this.size-1),this.keyAt(i)}find(e,t){let r=0;for(let i of this){if(Reflect.apply(e,t,[i,r,this]))return i;r++}}findIndex(e,t){let r=0;for(let i of this){if(Reflect.apply(e,t,[i,r,this]))return r;r++}return -1}filter(t,r){let i=[],n=0;for(let e of this)Reflect.apply(t,r,[e,n,this])&&i.push(e),n++;return new e(i)}map(t,r){let i=[],n=0;for(let e of this)i.push([e[0],Reflect.apply(t,r,[e,n,this])]),n++;return new e(i)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[i,n]=t,l=0,s=null!=n?n:this.at(0);for(let e of this)s=0===l&&1===t.length?e:Reflect.apply(i,this,[s,e,l,this]),l++;return s}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[i,n]=t,l=null!=n?n:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);l=e===this.size-1&&1===t.length?r:Reflect.apply(i,this,[l,r,e,this])}return l}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),i=this.get(r);t.set(r,i)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];let n=[...this.entries()];return n.splice(...r),new e(n)}slice(t,r){let i=new e,n=this.size-1;if(void 0===t)return i;t<0&&(t+=this.size),void 0!==r&&r>0&&(n=r-1);for(let e=t;e<=n;e++){let t=this.keyAt(e),r=this.get(t);i.set(t,r)}return i}every(e,t){let r=0;for(let i of this){if(!Reflect.apply(e,t,[i,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let i of this){if(Reflect.apply(e,t,[i,r,this]))return!0;r++}return!1}constructor(e){super(e),function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,i,{writable:!0,value:void 0}),a(this,i,[...super.keys()]),f.set(this,!0)}};var y=r(5185),v=r(5845),m=r(3655),x=r(2712),g=r(8905),A=r(1285),w="Collapsible",[b,k]=(0,l.A)(w),[R,C]=b(w),j=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:i,defaultOpen:l,disabled:s,onOpenChange:o,...a}=e,[d,c]=(0,v.i)({prop:i,defaultProp:null!=l&&l,onChange:o,caller:w});return(0,u.jsx)(R,{scope:r,disabled:s,contentId:(0,A.B)(),open:d,onOpenToggle:n.useCallback(()=>c(e=>!e),[c]),children:(0,u.jsx)(m.sG.div,{"data-state":_(d),"data-disabled":s?"":void 0,...a,ref:t})})});j.displayName=w;var N="CollapsibleTrigger",M=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...i}=e,n=C(N,r);return(0,u.jsx)(m.sG.button,{type:"button","aria-controls":n.contentId,"aria-expanded":n.open||!1,"data-state":_(n.open),"data-disabled":n.disabled?"":void 0,disabled:n.disabled,...i,ref:t,onClick:(0,y.m)(e.onClick,n.onOpenToggle)})});M.displayName=N;var O="CollapsibleContent",I=n.forwardRef((e,t)=>{let{forceMount:r,...i}=e,n=C(O,e.__scopeCollapsible);return(0,u.jsx)(g.C,{present:r||n.open,children:e=>{let{present:r}=e;return(0,u.jsx)(z,{...i,ref:t,present:r})}})});I.displayName=O;var z=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:i,children:l,...s}=e,o=C(O,r),[a,c]=n.useState(i),f=n.useRef(null),h=(0,d.s)(t,f),p=n.useRef(0),y=p.current,v=n.useRef(0),g=v.current,A=o.open||a,w=n.useRef(A),b=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.N)(()=>{let e=f.current;if(e){b.current=b.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();p.current=t.height,v.current=t.width,w.current||(e.style.transitionDuration=b.current.transitionDuration,e.style.animationName=b.current.animationName),c(i)}},[o.open,i]),(0,u.jsx)(m.sG.div,{"data-state":_(o.open),"data-disabled":o.disabled?"":void 0,id:o.contentId,hidden:!A,...s,ref:h,style:{"--radix-collapsible-content-height":y?"".concat(y,"px"):void 0,"--radix-collapsible-content-width":g?"".concat(g,"px"):void 0,...e.style},children:A&&l})});function _(e){return e?"open":"closed"}var D=n.createContext(void 0),S="Accordion",E=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[T,q,H]=function(e){let t=e+"CollectionProvider",[r,i]=(0,l.A)(t),[s,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,i=n.useRef(null),l=n.useRef(new Map).current;return(0,u.jsx)(s,{scope:t,itemMap:l,collectionRef:i,children:r})};a.displayName=t;let f=e+"CollectionSlot",h=(0,c.TL)(f),p=n.forwardRef((e,t)=>{let{scope:r,children:i}=e,n=o(f,r),l=(0,d.s)(t,n.collectionRef);return(0,u.jsx)(h,{ref:l,children:i})});p.displayName=f;let y=e+"CollectionItemSlot",v="data-radix-collection-item",m=(0,c.TL)(y),x=n.forwardRef((e,t)=>{let{scope:r,children:i,...l}=e,s=n.useRef(null),a=(0,d.s)(t,s),c=o(y,r);return n.useEffect(()=>(c.itemMap.set(s,{ref:s,...l}),()=>void c.itemMap.delete(s))),(0,u.jsx)(m,{...{[v]:""},ref:a,children:i})});return x.displayName=y,[{Provider:a,Slot:p,ItemSlot:x},function(t){let r=o(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},i]}(S),[L,P]=(0,l.A)(S,[H,k]),G=k(),B=n.forwardRef((e,t)=>{let{type:r,...i}=e;return(0,u.jsx)(T.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,u.jsx)(J,{...i,ref:t}):(0,u.jsx)(Y,{...i,ref:t})})});B.displayName=S;var[F,U]=L(S),[K,W]=L(S,{collapsible:!1}),Y=n.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:l=()=>{},collapsible:s=!1,...o}=e,[a,d]=(0,v.i)({prop:r,defaultProp:null!=i?i:"",onChange:l,caller:S});return(0,u.jsx)(F,{scope:e.__scopeAccordion,value:n.useMemo(()=>a?[a]:[],[a]),onItemOpen:d,onItemClose:n.useCallback(()=>s&&d(""),[s,d]),children:(0,u.jsx)(K,{scope:e.__scopeAccordion,collapsible:s,children:(0,u.jsx)(X,{...o,ref:t})})})}),J=n.forwardRef((e,t)=>{let{value:r,defaultValue:i,onValueChange:l=()=>{},...s}=e,[o,a]=(0,v.i)({prop:r,defaultProp:null!=i?i:[],onChange:l,caller:S}),d=n.useCallback(e=>a(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[a]),c=n.useCallback(e=>a(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[a]);return(0,u.jsx)(F,{scope:e.__scopeAccordion,value:o,onItemOpen:d,onItemClose:c,children:(0,u.jsx)(K,{scope:e.__scopeAccordion,collapsible:!0,children:(0,u.jsx)(X,{...s,ref:t})})})}),[Q,V]=L(S),X=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:i,dir:l,orientation:s="vertical",...o}=e,a=n.useRef(null),c=(0,d.s)(a,t),f=q(r),h="ltr"===function(e){let t=n.useContext(D);return e||t||"ltr"}(l),p=(0,y.m)(e.onKeyDown,e=>{var t;if(!E.includes(e.key))return;let r=e.target,i=f().filter(e=>{var t;return!(null==(t=e.ref.current)?void 0:t.disabled)}),n=i.findIndex(e=>e.ref.current===r),l=i.length;if(-1===n)return;e.preventDefault();let o=n,a=l-1,d=()=>{(o=n+1)>a&&(o=0)},c=()=>{(o=n-1)<0&&(o=a)};switch(e.key){case"Home":o=0;break;case"End":o=a;break;case"ArrowRight":"horizontal"===s&&(h?d():c());break;case"ArrowDown":"vertical"===s&&d();break;case"ArrowLeft":"horizontal"===s&&(h?c():d());break;case"ArrowUp":"vertical"===s&&c()}null==(t=i[o%l].ref.current)||t.focus()});return(0,u.jsx)(Q,{scope:r,disabled:i,direction:l,orientation:s,children:(0,u.jsx)(T.Slot,{scope:r,children:(0,u.jsx)(m.sG.div,{...o,"data-orientation":s,ref:c,onKeyDown:i?void 0:p})})})}),Z="AccordionItem",[$,ee]=L(Z),et=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:i,...n}=e,l=V(Z,r),s=U(Z,r),o=G(r),a=(0,A.B)(),d=i&&s.value.includes(i)||!1,c=l.disabled||e.disabled;return(0,u.jsx)($,{scope:r,open:d,disabled:c,triggerId:a,children:(0,u.jsx)(j,{"data-orientation":l.orientation,"data-state":ea(d),...o,...n,ref:t,disabled:c,open:d,onOpenChange:e=>{e?s.onItemOpen(i):s.onItemClose(i)}})})});et.displayName=Z;var er="AccordionHeader",ei=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...i}=e,n=V(S,r),l=ee(er,r);return(0,u.jsx)(m.sG.h3,{"data-orientation":n.orientation,"data-state":ea(l.open),"data-disabled":l.disabled?"":void 0,...i,ref:t})});ei.displayName=er;var en="AccordionTrigger",el=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...i}=e,n=V(S,r),l=ee(en,r),s=W(en,r),o=G(r);return(0,u.jsx)(T.ItemSlot,{scope:r,children:(0,u.jsx)(M,{"aria-disabled":l.open&&!s.collapsible||void 0,"data-orientation":n.orientation,id:l.triggerId,...o,...i,ref:t})})});el.displayName=en;var es="AccordionContent",eo=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...i}=e,n=V(S,r),l=ee(es,r),s=G(r);return(0,u.jsx)(I,{role:"region","aria-labelledby":l.triggerId,"data-orientation":n.orientation,...s,...i,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function ea(e){return e?"open":"closed"}eo.displayName=es;var ed=B,ec=et,eu=ei,ef=el,eh=eo},6474:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7580:(e,t,r)=>{r.d(t,{A:()=>i});let i=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])}}]);