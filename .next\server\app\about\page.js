(()=>{var a={};a.id=220,a.ids=[220],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22118:(a,b,c)=>{Promise.resolve().then(c.bind(c,97918))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28770:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\gym\\\\src\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\app\\about\\page.tsx","default")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30366:(a,b,c)=>{Promise.resolve().then(c.bind(c,28770))},33219:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,28770)),"C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,44139)),"C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\app\\about\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/about/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41312:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86384:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687),e=c(72312);function f({title:a,subtitle:b,centered:c=!0}){return(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:`mb-12 ${c?"text-center":""}`,children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:a}),b&&(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:b})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},91527:(a,b,c)=>{"use strict";c.d(b,{r_:()=>h,FF:()=>g,KG:()=>i,$p:()=>d,rR:()=>f,DI:()=>e});let d=[{id:"1",name:"Personal Training",description:"One-on-one training sessions with certified trainers to help you achieve your fitness goals faster.",duration:"60 minutes",difficulty:"Beginner",timings:["6:00 AM - 7:00 AM","7:00 AM - 8:00 AM","5:00 PM - 6:00 PM","6:00 PM - 7:00 PM"],price:"NPR 2,500/session",image:"/images/services/personal-training.jpg"},{id:"2",name:"Group Fitness Classes",description:"High-energy group workouts including Zumba, Aerobics, and HIIT classes for all fitness levels.",duration:"45 minutes",difficulty:"Intermediate",timings:["6:30 AM - 7:15 AM","7:30 AM - 8:15 AM","5:30 PM - 6:15 PM","6:30 PM - 7:15 PM"],price:"NPR 1,500/month",image:"/images/services/group-fitness.jpg"},{id:"3",name:"Weight Training",description:"Comprehensive strength training with modern equipment and expert guidance for muscle building.",duration:"90 minutes",difficulty:"Advanced",timings:["5:00 AM - 6:30 AM","6:30 AM - 8:00 AM","4:00 PM - 5:30 PM","7:00 PM - 8:30 PM"],price:"NPR 3,000/month",image:"/images/services/weight-training.jpg"},{id:"4",name:"Cardio Training",description:"Improve your cardiovascular health with our state-of-the-art cardio equipment and programs.",duration:"60 minutes",difficulty:"Beginner",timings:["5:30 AM - 6:30 AM","7:00 AM - 8:00 AM","5:00 PM - 6:00 PM","7:30 PM - 8:30 PM"],price:"NPR 2,000/month",image:"/images/services/cardio.jpg"},{id:"5",name:"Yoga Classes",description:"Find inner peace and flexibility with our traditional and modern yoga sessions.",duration:"75 minutes",difficulty:"Beginner",timings:["6:00 AM - 7:15 AM","7:30 AM - 8:45 AM","5:00 PM - 6:15 PM"],price:"NPR 1,800/month",image:"/images/services/yoga.jpg"},{id:"6",name:"CrossFit Training",description:"High-intensity functional fitness training that builds strength, endurance, and agility.",duration:"60 minutes",difficulty:"Advanced",timings:["6:00 AM - 7:00 AM","7:00 AM - 8:00 AM","6:00 PM - 7:00 PM","7:00 PM - 8:00 PM"],price:"NPR 3,500/month",image:"/images/services/crossfit.jpg"}],e=[{id:"1",name:"Rajesh Shrestha",bio:"With over 8 years of experience in fitness training, Rajesh specializes in strength training and bodybuilding. He has helped hundreds of clients achieve their fitness goals.",specialization:["Strength Training","Bodybuilding","Powerlifting"],experience:"8+ years",image:"/images/trainers/rajesh.jpg",certifications:["NASM Certified Personal Trainer","Powerlifting Coach Level 2"]},{id:"2",name:"Priya Gurung",bio:"Priya is a certified yoga instructor and group fitness trainer. She brings positive energy and expertise to help clients find balance in their fitness journey.",specialization:["Yoga","Group Fitness","Flexibility Training"],experience:"6+ years",image:"/images/trainers/priya.jpg",certifications:["200-Hour Yoga Teacher Training","Group Fitness Instructor"]},{id:"3",name:"Amit Thapa",bio:"Former national athlete turned fitness trainer, Amit specializes in functional training and sports conditioning for athletes and fitness enthusiasts.",specialization:["Functional Training","Sports Conditioning","HIIT"],experience:"10+ years",image:"/images/trainers/amit.jpg",certifications:["ACSM Certified Exercise Physiologist","Functional Movement Screen"]},{id:"4",name:"Sunita Rai",bio:"Sunita is passionate about helping women achieve their fitness goals through personalized training programs and nutritional guidance.",specialization:["Women's Fitness","Weight Loss","Nutrition Coaching"],experience:"5+ years",image:"/images/trainers/sunita.jpg",certifications:["ACE Personal Trainer","Precision Nutrition Level 1"]},{id:"5",name:"Bikash Tamang",bio:"CrossFit enthusiast and certified trainer, Bikash brings high-intensity training expertise to help clients push their limits safely.",specialization:["CrossFit","Olympic Lifting","Metabolic Conditioning"],experience:"7+ years",image:"/images/trainers/bikash.jpg",certifications:["CrossFit Level 2 Trainer","USA Weightlifting Sports Performance Coach"]}],f=[{id:"1",name:"Suman Adhikari",rating:5,review:"FitZone Biratnagar has completely transformed my fitness journey. The trainers are knowledgeable and supportive, and the equipment is top-notch. I've lost 15kg in 6 months!",image:"/images/testimonials/suman.jpg",memberSince:"January 2023"},{id:"2",name:"Anita Sharma",rating:5,review:"The yoga classes here are amazing! Priya is an excellent instructor who creates a peaceful and motivating environment. I feel more flexible and stress-free than ever.",image:"/images/testimonials/anita.jpg",memberSince:"March 2023"},{id:"3",name:"Deepak Limbu",rating:4,review:"Great gym with modern facilities. The personal training sessions with Rajesh have helped me build muscle and improve my strength significantly. Highly recommended!",image:"/images/testimonials/deepak.jpg",memberSince:"August 2022"},{id:"4",name:"Kamala Devi",rating:5,review:"As a working mother, finding time for fitness was challenging. The flexible timings and supportive community at FitZone made it possible. I feel healthier and more energetic.",image:"/images/testimonials/kamala.jpg",memberSince:"June 2023"},{id:"5",name:"Ravi Chaudhary",rating:5,review:"The CrossFit classes are intense but incredibly rewarding. Bikash pushes us to our limits while ensuring proper form and safety. Best investment I've made for my health.",image:"/images/testimonials/ravi.jpg",memberSince:"November 2022"},{id:"6",name:"Meera Joshi",rating:4,review:"Clean facilities, friendly staff, and excellent group fitness classes. The community here is very welcoming and motivating. Love the morning Zumba sessions!",image:"/images/testimonials/meera.jpg",memberSince:"February 2023"}],g=[{id:"1",src:"/images/gallery/equipment-1.jpg",alt:"Modern weight training equipment",category:"equipment"},{id:"2",src:"/images/gallery/equipment-2.jpg",alt:"Cardio machines section",category:"equipment"},{id:"3",src:"/images/gallery/equipment-3.jpg",alt:"Free weights area",category:"equipment"},{id:"4",src:"/images/gallery/equipment-4.jpg",alt:"Functional training equipment",category:"equipment"},{id:"5",src:"/images/gallery/class-1.jpg",alt:"Group fitness class in session",category:"classes"},{id:"6",src:"/images/gallery/class-2.jpg",alt:"Yoga class in the studio",category:"classes"},{id:"7",src:"/images/gallery/class-3.jpg",alt:"CrossFit training session",category:"classes"},{id:"8",src:"/images/gallery/class-4.jpg",alt:"Personal training session",category:"classes"},{id:"9",src:"/images/gallery/facility-1.jpg",alt:"Main workout floor",category:"facilities"},{id:"10",src:"/images/gallery/facility-2.jpg",alt:"Locker room facilities",category:"facilities"},{id:"11",src:"/images/gallery/facility-3.jpg",alt:"Reception and waiting area",category:"facilities"},{id:"12",src:"/images/gallery/facility-4.jpg",alt:"Stretching and recovery area",category:"facilities"},{id:"13",src:"/images/gallery/event-1.jpg",alt:"Fitness challenge event",category:"events"},{id:"14",src:"/images/gallery/event-2.jpg",alt:"Member appreciation day",category:"events"},{id:"15",src:"/images/gallery/event-3.jpg",alt:"Nutrition workshop",category:"events"},{id:"16",src:"/images/gallery/event-4.jpg",alt:"Community fitness run",category:"events"}],h={address:"Main Road, Biratnagar-10, Morang, Nepal",phone:"+977-21-525-789",email:"<EMAIL>",hours:{weekdays:"5:00 AM - 9:00 PM",weekends:"6:00 AM - 8:00 PM"},socialMedia:{facebook:"https://facebook.com/fitzonebiratnagar",instagram:"https://instagram.com/fitzonebiratnagar",youtube:"https://youtube.com/@fitzonebiratnagar"}},i={name:"FitZone Biratnagar",tagline:"Transform Your Body, Transform Your Life",mission:"To provide world-class fitness facilities and expert guidance to help our community achieve their health and wellness goals.",established:"2020",members:"500+",trainers:"5",facilities:["Modern Weight Training Equipment","Cardio Machines","Group Fitness Studio","Yoga Studio","Locker Rooms","Parking Facility","Air Conditioning","Sound System"]}},97918:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>n});var d=c(60687),e=c(72312),f=c(44493),g=c(86384),h=c(91527),i=c(62688);let j=(0,i.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var k=c(41312);let l=(0,i.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var m=c(86561);function n(){let a=[{icon:j,title:"Excellence",description:"We strive for excellence in everything we do, from our equipment to our training programs."},{icon:k.A,title:"Community",description:"Building a supportive community where everyone feels welcome and motivated to achieve their goals."},{icon:l,title:"Wellness",description:"Promoting holistic wellness that encompasses physical, mental, and emotional health."},{icon:m.A,title:"Results",description:"Committed to helping our members achieve real, lasting results through proven methods."}];return(0,d.jsxs)("div",{className:"min-h-screen",children:[(0,d.jsx)("section",{className:"relative py-20 bg-gradient-to-r from-gray-900 to-gray-800 text-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,d.jsxs)(e.P.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-4xl md:text-5xl font-bold mb-6",children:["About ",h.KG.name]}),(0,d.jsxs)(e.P.p,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-xl text-gray-300 max-w-3xl mx-auto",children:["Established in ",h.KG.established,", we've been Biratnagar's premier fitness destination, helping over ",h.KG.members," achieve their health and wellness goals."]})]})}),(0,d.jsx)("section",{className:"py-16 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsx)(g.A,{title:"Our Story",subtitle:"From humble beginnings to Biratnagar's leading fitness center"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,d.jsx)(e.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,d.jsx)("div",{className:"aspect-video bg-gray-200 rounded-lg flex items-center justify-center mb-6",children:(0,d.jsx)("span",{className:"text-gray-500",children:"Gym Exterior Photo"})})}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-6",children:[(0,d.jsxs)("p",{className:"text-lg text-gray-600",children:[h.KG.name," was born from a simple vision: to create a fitness center that truly serves the Biratnagar community. Founded in ",h.KG.established,", we started with a commitment to providing world-class fitness facilities in the heart of our beloved city."]}),(0,d.jsx)("p",{className:"text-lg text-gray-600",children:"Over the years, we've grown from a small local gym to Biratnagar's most trusted fitness destination. Our success is measured not just in the modern equipment we've acquired or the facilities we've built, but in the lives we've transformed and the community we've created."}),(0,d.jsxs)("p",{className:"text-lg text-gray-600",children:["Today, with over ",h.KG.members," and a team of ",h.KG.trainers," certified trainers, we continue to evolve and adapt to meet the changing needs of our community while staying true to our core mission: ",h.KG.mission]})]})]})]})}),(0,d.jsx)("section",{className:"py-16 bg-gray-50",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsx)(g.A,{title:"Our Values",subtitle:"The principles that guide everything we do"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:a.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"h-full text-center hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(f.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4",children:(0,d.jsx)(a.icon,{className:"w-8 h-8 text-orange-600"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600",children:a.description})]})})},a.title))})]})}),(0,d.jsx)("section",{className:"py-16 bg-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsx)(g.A,{title:"Meet Our Team",subtitle:"Experienced professionals dedicated to your success"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12",children:h.DI.slice(0,3).map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(f.Zp,{className:"hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(f.Wu,{className:"p-6 text-center",children:[(0,d.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-gray-500 text-sm font-semibold",children:a.name.split(" ").map(a=>a[0]).join("")})}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:a.name}),(0,d.jsxs)("p",{className:"text-orange-600 font-medium mb-3",children:[a.experience," Experience"]}),(0,d.jsxs)("p",{className:"text-gray-600 text-sm mb-4",children:[a.bio.substring(0,100),"..."]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:a.specialization.slice(0,2).map((a,b)=>(0,d.jsx)("span",{className:"px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full",children:a},b))})]})})},a.id))}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center",children:(0,d.jsx)("a",{href:"/trainers",className:"inline-flex items-center px-6 py-3 border border-orange-600 text-orange-600 font-medium rounded-lg hover:bg-orange-600 hover:text-white transition-colors duration-200",children:"Meet All Our Trainers"})})]})})]})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,189,674],()=>b(b.s=33219));module.exports=c})();