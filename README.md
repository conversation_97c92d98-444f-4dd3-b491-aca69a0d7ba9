# FitZone Biratnagar - Gym Website

A modern, responsive website for FitZone Biratnagar, a premier fitness center located in Biratnagar, Nepal. Built with Next.js, Tailwind CSS, and ShadCN UI components.

## 🌟 Features

- **Responsive Design**: Mobile-first approach with seamless experience across all devices
- **Modern UI**: Clean, energetic design with smooth animations using Framer Motion
- **SEO Optimized**: Optimized for search engines with proper metadata and sitemap
- **Accessibility**: Built with accessibility best practices
- **Performance**: Fast loading times with Next.js optimization

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Styling**: Tailwind CSS
- **UI Components**: ShadCN UI
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **TypeScript**: Full type safety

## 📱 Pages

1. **Home** - Hero section, services highlights, facilities showcase, testimonials
2. **About** - Gym story, values, team preview
3. **Services** - Detailed service listings with pricing
4. **Trainers** - Certified trainer profiles
5. **Gallery** - Photo gallery with lightbox functionality
6. **Testimonials** - Member success stories and reviews
7. **Contact** - Contact form, location, and social media links

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd gym
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── about/
│   ├── contact/
│   ├── gallery/
│   ├── services/
│   ├── testimonials/
│   ├── trainers/
│   ├── globals.css
│   ├── layout.tsx
│   ├── loading.tsx
│   ├── not-found.tsx
│   ├── page.tsx
│   ├── robots.ts
│   └── sitemap.ts
├── components/
│   ├── layout/             # Header, Footer, Layout components
│   ├── sections/           # Reusable page sections
│   └── ui/                 # ShadCN UI components
├── data/                   # Mock data and constants
├── lib/                    # Utility functions
└── types/                  # TypeScript type definitions
```

## 🎨 Design Features

- **Color Scheme**: Orange/red gradient accents with clean grays
- **Typography**: Inter font for modern readability
- **Animations**: Smooth page transitions and hover effects
- **Components**: Reusable card layouts and consistent spacing

## 📊 SEO Features

- Optimized meta titles and descriptions
- Structured data markup
- XML sitemap generation
- Robots.txt configuration
- Open Graph tags for social sharing

## 🔧 Customization

### Adding New Services

1. Update `src/data/services.ts`
2. Add service images to `public/images/services/`

### Adding New Trainers

1. Update `src/data/trainers.ts`
2. Add trainer photos to `public/images/trainers/`

### Updating Contact Information

1. Modify `src/data/contact.ts`
2. Update social media links and contact details

## 📱 Responsive Breakpoints

- Mobile: 320px - 768px
- Tablet: 768px - 1024px
- Desktop: 1024px+

## 🚀 Deployment

### Build for Production

```bash
npm run build
npm start
```

### Deploy to Vercel

1. Push code to GitHub
2. Connect repository to Vercel
3. Deploy automatically

## 📄 License

This project is created for FitZone Biratnagar. All rights reserved.

## 🤝 Contributing

This is a custom website for FitZone Biratnagar. For modifications or updates, please contact the development team.

## 📞 Support

For technical support or website updates, please contact:
- Email: <EMAIL>
- Phone: +977-21-525-789

---

Built with ❤️ for FitZone Biratnagar community
