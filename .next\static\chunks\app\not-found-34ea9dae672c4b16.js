(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155);r(2115);var s=r(9708),i=r(2085),n=r(9434);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:i,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:i,className:t})),...l})}},1634:(e,t,r)=>{Promise.resolve().then(r.bind(r,1906))},1906:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(5155),s=r(2984),i=r(6874),n=r.n(i),o=r(285),d=r(465),l=r(9946);let c=(0,l.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),h=(0,l.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function x(){return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(s.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6},className:"mb-8",children:[(0,a.jsx)(d.A,{className:"w-24 h-24 text-orange-600 mx-auto mb-6"}),(0,a.jsx)("h1",{className:"text-6xl font-bold text-gray-900 mb-4",children:"404"}),(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Page Not Found"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-8 max-w-md mx-auto",children:"Looks like you've wandered off the fitness path! The page you're looking for doesn't exist."})]}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(o.$,{asChild:!0,size:"lg",className:"bg-orange-600 hover:bg-orange-700",children:(0,a.jsxs)(n(),{href:"/",children:[(0,a.jsx)(c,{className:"w-4 h-4 mr-2"}),"Go Home"]})}),(0,a.jsx)(o.$,{asChild:!0,variant:"outline",size:"lg",onClick:()=>window.history.back(),children:(0,a.jsxs)("span",{className:"cursor-pointer",children:[(0,a.jsx)(h,{className:"w-4 h-4 mr-2"}),"Go Back"]})})]}),(0,a.jsxs)(s.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.6},className:"mt-12",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"Need help finding what you're looking for?"}),(0,a.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 text-sm",children:[(0,a.jsx)(n(),{href:"/about",className:"text-orange-600 hover:text-orange-700 underline",children:"About Us"}),(0,a.jsx)(n(),{href:"/services",className:"text-orange-600 hover:text-orange-700 underline",children:"Our Services"}),(0,a.jsx)(n(),{href:"/trainers",className:"text-orange-600 hover:text-orange-700 underline",children:"Meet Our Trainers"}),(0,a.jsx)(n(),{href:"/contact",className:"text-orange-600 hover:text-orange-700 underline",children:"Contact Us"})]})]})]})})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(2596),s=r(9688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{e.O(0,[813,277,470,441,964,358],()=>e(e.s=1634)),_N_E=e.O()}]);