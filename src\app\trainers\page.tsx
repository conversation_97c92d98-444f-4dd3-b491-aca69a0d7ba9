'use client';

import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import SectionTitle from '@/components/sections/SectionTitle';
import { trainers } from '@/data';
import { Award, Clock, Star } from 'lucide-react';

export default function TrainersPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-gray-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold mb-6"
          >
            Meet Our Expert Trainers
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Our certified fitness professionals are here to guide, motivate, and help you achieve 
            your fitness goals with personalized attention and expertise.
          </motion.p>
        </div>
      </section>

      {/* Trainers Grid */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionTitle
            title="Your Fitness Journey Starts Here"
            subtitle="Get to know the passionate professionals who will help transform your life"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {trainers.map((trainer, index) => (
              <motion.div
                key={trainer.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="text-center">
                    <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <span className="text-gray-500 text-lg font-semibold">
                        {trainer.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <CardTitle className="text-xl text-gray-900">{trainer.name}</CardTitle>
                    <CardDescription className="text-orange-600 font-semibold flex items-center justify-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {trainer.experience} Experience
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-6 text-center">{trainer.bio}</p>
                    
                    {/* Specializations */}
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <Star className="w-4 h-4 mr-2 text-orange-600" />
                        Specializations
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {trainer.specialization.map((spec, i) => (
                          <span 
                            key={i} 
                            className="px-3 py-1 bg-orange-100 text-orange-600 text-sm rounded-full"
                          >
                            {spec}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    {/* Certifications */}
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                        <Award className="w-4 h-4 mr-2 text-orange-600" />
                        Certifications
                      </h4>
                      <ul className="space-y-1">
                        {trainer.certifications.map((cert, i) => (
                          <li key={i} className="text-sm text-gray-600 flex items-start">
                            <div className="w-1.5 h-1.5 bg-orange-600 rounded-full mt-2 mr-2 flex-shrink-0"></div>
                            {cert}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <Button className="w-full bg-orange-600 hover:bg-orange-700">
                      Book Session with {trainer.name.split(' ')[0]}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Our Trainers */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionTitle
            title="Why Choose Our Trainers?"
            subtitle="Experience the difference that professional guidance makes"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: 'Certified Professionals',
                description: 'All our trainers hold recognized certifications and continue their education.',
                icon: '🎓'
              },
              {
                title: 'Personalized Approach',
                description: 'Every workout plan is tailored to your specific goals and fitness level.',
                icon: '🎯'
              },
              {
                title: 'Proven Results',
                description: 'Our trainers have helped hundreds achieve their fitness transformations.',
                icon: '📈'
              },
              {
                title: 'Ongoing Support',
                description: 'Get motivation, guidance, and support throughout your entire journey.',
                icon: '🤝'
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-orange-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Work with Our Expert Trainers?
            </h2>
            <p className="text-xl mb-8 text-orange-100 max-w-2xl mx-auto">
              Take the first step towards your fitness goals. Book a consultation with one of our 
              certified trainers today and start your transformation journey.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-orange-600 hover:bg-gray-100">
                <a href="/contact">Book Consultation</a>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-orange-600">
                <a href="/services">View Training Programs</a>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
