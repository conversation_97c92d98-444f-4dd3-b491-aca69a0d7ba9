exports.id=674,exports.ids=[674],exports.modules={3310:(a,b,c)=>{Promise.resolve().then(c.bind(c,54413))},4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},4924:(a,b,c)=>{Promise.resolve().then(c.bind(c,52027))},15736:(a,b,c)=>{"use strict";c.d(b,{default:()=>n});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(72312),i=c(21067),j=c(11860),k=c(12941),l=c(29523);let m=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Trainers",href:"/trainers"},{name:"Gallery",href:"/gallery"},{name:"Testimonials",href:"/testimonials"},{name:"Contact",href:"/contact"}];function n(){let[a,b]=(0,e.useState)(!1);return(0,d.jsx)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,d.jsxs)("nav",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8","aria-label":"Top",children:[(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)(g(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)(i.A,{className:"h-8 w-8 text-orange-600"}),(0,d.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"FitZone Biratnagar"})]})}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsx)("div",{className:"ml-10 flex items-baseline space-x-4",children:m.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-gray-700 hover:text-orange-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200",children:a.name},a.name))})}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsx)(l.$,{asChild:!0,className:"bg-orange-600 hover:bg-orange-700",children:(0,d.jsx)(g(),{href:"/contact",children:"Join Now"})})}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>b(!a),children:a?(0,d.jsx)(j.A,{className:"h-6 w-6"}):(0,d.jsx)(k.A,{className:"h-6 w-6"})})})]}),a&&(0,d.jsx)(h.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden",children:(0,d.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",children:[m.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-gray-700 hover:text-orange-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>b(!1),children:a.name},a.name)),(0,d.jsx)("div",{className:"pt-2",children:(0,d.jsx)(l.$,{asChild:!0,className:"w-full bg-orange-600 hover:bg-orange-700",children:(0,d.jsx)(g(),{href:"/contact",onClick:()=>b(!1),children:"Join Now"})})})]})})]})})}},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},44139:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>w,metadata:()=>v});var d=c(37413),e=c(25091),f=c.n(e);c(61135);var g=c(68926),h=c(4536),i=c.n(h),j=c(95585),k=c(45868),l=c(56378),m=c(57803),n=c(49046),o=c(71750),p=c(60343),q=c(53148);let r={address:"Main Road, Biratnagar-10, Morang, Nepal",phone:"+977-21-525-789",email:"<EMAIL>",hours:{weekdays:"5:00 AM - 9:00 PM",weekends:"6:00 AM - 8:00 PM"},socialMedia:{facebook:"https://facebook.com/fitzonebiratnagar",instagram:"https://instagram.com/fitzonebiratnagar",youtube:"https://youtube.com/@fitzonebiratnagar"}},s={name:"FitZone Biratnagar",mission:"To provide world-class fitness facilities and expert guidance to help our community achieve their health and wellness goals."};function t(){return(0,d.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(j.A,{className:"h-8 w-8 text-orange-600"}),(0,d.jsx)("span",{className:"text-xl font-bold",children:s.name})]}),(0,d.jsx)("p",{className:"text-gray-300 text-sm",children:s.mission}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)(i(),{href:r.socialMedia.facebook,className:"text-gray-400 hover:text-orange-600 transition-colors",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)(k.A,{className:"h-5 w-5"})}),(0,d.jsx)(i(),{href:r.socialMedia.instagram,className:"text-gray-400 hover:text-orange-600 transition-colors",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)(l.A,{className:"h-5 w-5"})}),(0,d.jsx)(i(),{href:r.socialMedia.youtube,className:"text-gray-400 hover:text-orange-600 transition-colors",target:"_blank",rel:"noopener noreferrer",children:(0,d.jsx)(m.A,{className:"h-5 w-5"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Quick Links"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/about",className:"text-gray-300 hover:text-orange-600 transition-colors",children:"About Us"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/services",className:"text-gray-300 hover:text-orange-600 transition-colors",children:"Services"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/trainers",className:"text-gray-300 hover:text-orange-600 transition-colors",children:"Our Trainers"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/gallery",className:"text-gray-300 hover:text-orange-600 transition-colors",children:"Gallery"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/testimonials",className:"text-gray-300 hover:text-orange-600 transition-colors",children:"Testimonials"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Contact Info"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-orange-600 mt-0.5"}),(0,d.jsx)("span",{className:"text-gray-300 text-sm",children:r.address})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(o.A,{className:"h-5 w-5 text-orange-600"}),(0,d.jsx)("span",{className:"text-gray-300 text-sm",children:r.phone})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(p.A,{className:"h-5 w-5 text-orange-600"}),(0,d.jsx)("span",{className:"text-gray-300 text-sm",children:r.email})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Opening Hours"}),(0,d.jsx)("div",{className:"space-y-3",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(q.A,{className:"h-5 w-5 text-orange-600 mt-0.5"}),(0,d.jsxs)("div",{className:"text-gray-300 text-sm",children:[(0,d.jsxs)("div",{children:["Mon - Fri: ",r.hours.weekdays]}),(0,d.jsxs)("div",{children:["Sat - Sun: ",r.hours.weekends]})]})]})})]})]}),(0,d.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:(0,d.jsxs)("p",{className:"text-gray-400 text-sm",children:["\xa9 ",new Date().getFullYear()," ",s.name,". All rights reserved."]})})]})})}function u({children:a}){return(0,d.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,d.jsx)(g.default,{}),(0,d.jsx)("main",{className:"flex-grow",children:a}),(0,d.jsx)(t,{})]})}let v={title:"FitZone Biratnagar - Premier Fitness Center in Biratnagar, Nepal",description:"Transform your body and life at FitZone Biratnagar. Premier gym with modern equipment, expert trainers, and diverse fitness programs in Biratnagar, Nepal.",keywords:"gym Biratnagar, fitness center Biratnagar, personal training Biratnagar, yoga classes Nepal, weight training, CrossFit Biratnagar",authors:[{name:"FitZone Biratnagar"}],openGraph:{title:"FitZone Biratnagar - Premier Fitness Center",description:"Transform your body and life at FitZone Biratnagar. Modern equipment, expert trainers, diverse fitness programs.",type:"website",locale:"en_US"}};function w({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} font-sans antialiased`,children:(0,d.jsx)(u,{children:a})})})}},46780:(a,b,c)=>{Promise.resolve().then(c.bind(c,96753))},52027:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(72312),f=c(21067);function g(){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-white",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)(e.P.div,{animate:{rotate:360,scale:[1,1.2,1]},transition:{rotate:{duration:2,repeat:1/0,ease:"linear"},scale:{duration:1,repeat:1/0,ease:"easeInOut"}},className:"inline-block mb-4",children:(0,d.jsx)(f.A,{className:"w-12 h-12 text-orange-600"})}),(0,d.jsx)(e.P.h2,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},className:"text-xl font-semibold text-gray-900 mb-2",children:"FitZone Biratnagar"}),(0,d.jsx)(e.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"flex space-x-1 justify-center",children:[0,1,2].map(a=>(0,d.jsx)(e.P.div,{animate:{y:[0,-10,0],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,delay:.2*a},className:"w-2 h-2 bg-orange-600 rounded-full"},a))})]})})}},52432:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,68926))},54413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\gym\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\app\\not-found.tsx","default")},57347:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l});var d=c(60687),e=c(72312),f=c(85814),g=c.n(f),h=c(29523),i=c(21067),j=c(32192),k=c(28559);function l(){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,d.jsxs)("div",{className:"text-center px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6},className:"mb-8",children:[(0,d.jsx)(i.A,{className:"w-24 h-24 text-orange-600 mx-auto mb-6"}),(0,d.jsx)("h1",{className:"text-6xl font-bold text-gray-900 mb-4",children:"404"}),(0,d.jsx)("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Page Not Found"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 mb-8 max-w-md mx-auto",children:"Looks like you've wandered off the fitness path! The page you're looking for doesn't exist."})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(h.$,{asChild:!0,size:"lg",className:"bg-orange-600 hover:bg-orange-700",children:(0,d.jsxs)(g(),{href:"/",children:[(0,d.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"Go Home"]})}),(0,d.jsx)(h.$,{asChild:!0,variant:"outline",size:"lg",onClick:()=>window.history.back(),children:(0,d.jsxs)("span",{className:"cursor-pointer",children:[(0,d.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Go Back"]})})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.6,delay:.6},className:"mt-12",children:[(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"Need help finding what you're looking for?"}),(0,d.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 text-sm",children:[(0,d.jsx)(g(),{href:"/about",className:"text-orange-600 hover:text-orange-700 underline",children:"About Us"}),(0,d.jsx)(g(),{href:"/services",className:"text-orange-600 hover:text-orange-700 underline",children:"Our Services"}),(0,d.jsx)(g(),{href:"/trainers",className:"text-orange-600 hover:text-orange-700 underline",children:"Meet Our Trainers"}),(0,d.jsx)(g(),{href:"/contact",className:"text-orange-600 hover:text-orange-700 underline",children:"Contact Us"})]})]})]})})}},61135:()=>{},66358:(a,b,c)=>{Promise.resolve().then(c.bind(c,57347))},67393:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(37413),e=c(96753);function f(){return(0,d.jsx)(e.default,{})}},68926:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\gym\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\components\\layout\\Header.tsx","default")},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},70928:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,15736))},83940:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},96753:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\gym\\\\src\\\\components\\\\ui\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\gym\\src\\components\\ui\\loading.tsx","default")},97444:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))}};