'use client';

import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import SectionTitle from '@/components/sections/SectionTitle';
import { services } from '@/data';
import { Clock, TrendingUp, Users, DollarSign } from 'lucide-react';

export default function ServicesPage() {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-600';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-600';
      case 'Advanced':
        return 'bg-red-100 text-red-600';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-orange-600 to-red-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl font-bold mb-6"
          >
            Our Services & Classes
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl text-orange-100 max-w-3xl mx-auto"
          >
            Discover our comprehensive range of fitness programs designed to help you achieve your goals, 
            no matter your fitness level or experience.
          </motion.p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionTitle
            title="Choose Your Path to Fitness"
            subtitle="From personal training to group classes, we have something for everyone"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <div className="aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                      <span className="text-gray-500 text-sm">{service.name} Image</span>
                    </div>
                    <CardTitle className="text-xl text-gray-900">{service.name}</CardTitle>
                    <CardDescription className="text-orange-600 font-semibold text-lg">
                      {service.price}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-6">{service.description}</p>
                    
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="w-4 h-4 mr-2 text-orange-600" />
                        <span><strong>Duration:</strong> {service.duration}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <TrendingUp className="w-4 h-4 mr-2 text-orange-600" />
                        <span><strong>Level:</strong></span>
                        <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getDifficultyColor(service.difficulty)}`}>
                          {service.difficulty}
                        </span>
                      </div>
                    </div>
                    
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="timings">
                        <AccordionTrigger className="text-sm font-medium">
                          Available Timings
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-2">
                            {service.timings.map((timing, i) => (
                              <div key={i} className="flex items-center text-sm text-gray-600">
                                <div className="w-2 h-2 bg-orange-600 rounded-full mr-2"></div>
                                {timing}
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Info */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionTitle
            title="Flexible Pricing Options"
            subtitle="Choose the plan that works best for your lifestyle and budget"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-8">
                  <Users className="w-12 h-12 text-orange-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Group Classes</h3>
                  <p className="text-gray-600 mb-4">Perfect for those who love working out with others</p>
                  <div className="text-2xl font-bold text-orange-600 mb-4">NPR 1,500 - 2,000/month</div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• Unlimited group classes</li>
                    <li>• Yoga, Zumba, HIIT</li>
                    <li>• Motivating community</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-shadow duration-300 border-orange-600 border-2">
                <CardContent className="p-8">
                  <TrendingUp className="w-12 h-12 text-orange-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Personal Training</h3>
                  <p className="text-gray-600 mb-4">One-on-one attention for faster results</p>
                  <div className="text-2xl font-bold text-orange-600 mb-4">NPR 2,500/session</div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• Customized workout plans</li>
                    <li>• Individual attention</li>
                    <li>• Faster goal achievement</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-8">
                  <DollarSign className="w-12 h-12 text-orange-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Premium Access</h3>
                  <p className="text-gray-600 mb-4">Full access to all facilities and services</p>
                  <div className="text-2xl font-bold text-orange-600 mb-4">NPR 4,000/month</div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• All equipment access</li>
                    <li>• All group classes</li>
                    <li>• Priority booking</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </div>
          
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <Button asChild size="lg" className="bg-orange-600 hover:bg-orange-700">
              <a href="/contact">Get Started Today</a>
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
