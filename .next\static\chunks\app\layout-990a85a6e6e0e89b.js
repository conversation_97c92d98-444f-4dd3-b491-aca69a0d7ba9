(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,a,s)=>{"use strict";s.d(a,{$:()=>l});var r=s(5155);s(2115);var t=s(9708),n=s(2085),i=s(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:s,size:n,asChild:l=!1,...o}=e,c=l?t.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:n,className:a})),...o})}},347:()=>{},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},3688:(e,a,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.t.bind(s,1666,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,7035))},4416:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},7035:(e,a,s)=>{"use strict";s.d(a,{default:()=>x});var r=s(5155),t=s(2115),n=s(6874),i=s.n(n),d=s(2984),l=s(465),o=s(4416);let c=(0,s(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var h=s(285);let m=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Trainers",href:"/trainers"},{name:"Gallery",href:"/gallery"},{name:"Testimonials",href:"/testimonials"},{name:"Contact",href:"/contact"}];function x(){let[e,a]=(0,t.useState)(!1);return(0,r.jsx)("header",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,r.jsxs)("nav",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8","aria-label":"Top",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)(l.A,{className:"h-8 w-8 text-orange-600"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"FitZone Biratnagar"})]})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)("div",{className:"ml-10 flex items-baseline space-x-4",children:m.map(e=>(0,r.jsx)(i(),{href:e.href,className:"text-gray-700 hover:text-orange-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200",children:e.name},e.name))})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)(h.$,{asChild:!0,className:"bg-orange-600 hover:bg-orange-700",children:(0,r.jsx)(i(),{href:"/contact",children:"Join Now"})})}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>a(!e),children:e?(0,r.jsx)(o.A,{className:"h-6 w-6"}):(0,r.jsx)(c,{className:"h-6 w-6"})})})]}),e&&(0,r.jsx)(d.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",children:[m.map(e=>(0,r.jsx)(i(),{href:e.href,className:"text-gray-700 hover:text-orange-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>a(!1),children:e.name},e.name)),(0,r.jsx)("div",{className:"pt-2",children:(0,r.jsx)(h.$,{asChild:!0,className:"w-full bg-orange-600 hover:bg-orange-700",children:(0,r.jsx)(i(),{href:"/contact",onClick:()=>a(!1),children:"Join Now"})})})]})})]})})}},9434:(e,a,s)=>{"use strict";s.d(a,{cn:()=>n});var r=s(2596),t=s(9688);function n(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,t.QP)((0,r.$)(a))}}},e=>{e.O(0,[258,813,277,470,441,964,358],()=>e(e.s=3688)),_N_E=e.O()}]);